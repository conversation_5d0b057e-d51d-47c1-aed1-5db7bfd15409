# UserInfo Service API 文档

## 服务类全名
`com.yy.gameecology.activity.service.UserInfoService`

## 方法说明

### 1. getUserInfo 方法
**功能**：获取单个用户信息

**参数**：
- `uid` (long): 用户ID

**返回类型**：`UserInfoVo` (用户信息对象)

**使用示例**：
```java
UserInfoVo user = userInfoService.getUserInfo(12345L);
```

### 2. getUserInfo (批量) 方法
**功能**：批量获取用户信息

**参数**：
- `uids` (Collection<Long>): 用户ID列表

**返回类型**：`Map<Long, UserInfoVo>` (用户信息映射表)

**使用示例**：
```java
Map<Long, UserInfoVo> users = userInfoService.getUserInfo(Arrays.asList(12345L, 67890L));
```

### 3. getUserInfoWithNickExt 方法（多昵称版本）
**功能**：获取带扩展昵称的用户信息，支持多平台昵称获取

**方法重载版本**：
1. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, boolean nickBase64, int templateType)` - 完整版本
2. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers)` - 简化版本
3. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, int templateType)` - 指定模板版本

**完整版本参数**：
- `uids` (List<Long>): 用户ID列表
- `multiNickUsers` (Map<String, Map<String, MultiNickItem>>): 用于接收多昵称扩展数据的容器，方法执行后会填充数据
- `nickBase64` (boolean): 是否对昵称进行Base64编码
- `templateType` (int): 模板类型代码，参考Template枚举

**返回类型**：`Map<Long, UserInfoVo>` (用户信息映射表)

**核心特性**：
- 自动分批处理：单次最大查询499个用户，超出会自动分批
- 多平台昵称：支持获取用户在不同平台（YY、百度、贴吧等）的昵称
- Base64编码：可选择对昵称进行Base64编码处理
- 模板支持：根据不同业务模板获取对应的用户信息

**使用示例**：
```java
// 基础用法 - 获取多昵称信息
List<Long> userIds = Arrays.asList(12345L, 67890L, 11111L);
Map<String, Map<String, MultiNickItem>> multiNickData = new HashMap<>();
Map<Long, UserInfoVo> users = userInfoService.getUserInfoWithNickExt(
    userIds,
    multiNickData,
    false,
    Template.all.getCode()
);

// 处理多昵称数据
for (String uid : multiNickData.keySet()) {
    Map<String, MultiNickItem> platformNicks = multiNickData.get(uid);
    for (String platform : platformNicks.keySet()) {
        MultiNickItem nickItem = platformNicks.get(platform);
        System.out.println("用户" + uid + "在" + platform + "平台的昵称: " + nickItem.getNick());
    }
}

// Base64编码昵称
Map<Long, UserInfoVo> usersWithBase64Nick = userInfoService.getUserInfoWithNickExt(
    userIds,
    multiNickData,
    true,  // 启用Base64编码
    Template.gamebaby.getCode()
);

// 简化用法 - 使用默认参数
Map<String, Map<String, MultiNickItem>> simpleNickData = new HashMap<>();
Map<Long, UserInfoVo> simpleUsers = userInfoService.getUserInfoWithNickExt(userIds, simpleNickData);
```

## 数据结构说明

### UserInfoVo 类
```java
public class UserInfoVo {
    private long uid;          // 用户ID
    private String nick;       // 用户昵称
    private String avatarUrl;  // 头像URL
    private int gender;        // 性别
    private int vipLevel;      // VIP等级
    // ...其他字段
}
```

### MultiNickItem 类
```java
public class MultiNickItem {
    private String nick;      // 昵称
    private String color;     // 颜色值
    private int fontSize;     // 字体大小
    // ...其他样式字段
}
```

## 注意事项
1. 批量查询时建议控制每次查询的用户数量(建议不超过100个)
2. 带样式昵称查询会增加响应时间，非必要不使用
3. 模板代码需参考Template枚举定义

## 最佳实践示例
```java
// 批量查询优化示例
List<Long> userIds = getLargeUserList(); // 假设返回大量用户ID
Map<Long, UserInfoVo> allUsers = new HashMap<>();

// 分批查询避免性能问题
Lists.partition(userIds, 100).forEach(batch -> {
    allUsers.putAll(userInfoService.getUserInfo(batch));
});
```