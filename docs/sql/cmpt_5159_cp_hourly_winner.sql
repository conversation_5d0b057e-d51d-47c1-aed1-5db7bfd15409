-- CP小时第一名玩法组件数据库表
-- 组件ID: 5159

CREATE TABLE `cmpt_5159_cp_hourly_winner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `act_id` bigint(20) NOT NULL COMMENT '活动ID',
  `cmpt_use_inx` bigint(20) NOT NULL COMMENT '组件使用索引',
  `seq` varchar(128) NOT NULL COMMENT '事件序列号，用于幂等性控制',
  `rank_id` bigint(20) NOT NULL COMMENT '榜单ID',
  `phase_id` bigint(20) NOT NULL COMMENT '阶段ID',
  `hour_code` varchar(32) NOT NULL COMMENT '小时编码，格式：yyyyMMddHH',
  `user_uid` bigint(20) NOT NULL COMMENT '用户UID',
  `anchor_uid` bigint(20) NOT NULL COMMENT '主播UID',
  `cp_score` bigint(20) NOT NULL COMMENT 'CP分数',
  `user_nick` varchar(128) DEFAULT NULL COMMENT '用户昵称',
  `user_avatar` varchar(512) DEFAULT NULL COMMENT '用户头像',
  `anchor_nick` varchar(128) DEFAULT NULL COMMENT '主播昵称',
  `anchor_avatar` varchar(512) DEFAULT NULL COMMENT '主播头像',
  `user_multi_nick` text DEFAULT NULL COMMENT '用户多昵称信息JSON',
  `anchor_multi_nick` text DEFAULT NULL COMMENT '主播多昵称信息JSON',
  `award_amount` bigint(20) DEFAULT 0 COMMENT '奖励金额，单位：厘',
  `award_config` text DEFAULT NULL COMMENT '奖励配置JSON',
  `gift_pool_used` bigint(20) DEFAULT 0 COMMENT '使用的礼物奖池金额，单位：厘',
  `is_pool_sufficient` tinyint(1) DEFAULT 1 COMMENT '奖池是否充足：1-充足，0-不足',
  `channel_sid` bigint(20) DEFAULT NULL COMMENT '房间SID',
  `channel_ssid` bigint(20) DEFAULT NULL COMMENT '房间SSID',
  `watchword_sent` tinyint(1) DEFAULT 0 COMMENT '是否已发送应援口令：1-已发送，0-未发送',
  `broadcast_sent` tinyint(1) DEFAULT 0 COMMENT '是否已广播：1-已广播，0-未广播',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_act_cmpt_seq` (`act_id`, `cmpt_use_inx`, `seq`),
  KEY `idx_act_cmpt_hour` (`act_id`, `cmpt_use_inx`, `hour_code`),
  KEY `idx_act_cmpt_rank_phase` (`act_id`, `cmpt_use_inx`, `rank_id`, `phase_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CP小时第一名玩法组件数据表';
