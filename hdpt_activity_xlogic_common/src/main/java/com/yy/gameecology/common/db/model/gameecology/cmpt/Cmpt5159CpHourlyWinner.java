package com.yy.gameecology.common.db.model.gameecology.cmpt;

import lombok.Data;

import java.util.Date;

/**
 * CP小时第一名玩法组件数据模型
 * 对应表：cmpt_5159_cp_hourly_winner
 * 
 * <AUTHOR> Generated
 * @date 2025-07-22
 */
@Data
public class Cmpt5159CpHourlyWinner {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 活动ID
     */
    private Long actId;
    
    /**
     * 组件使用索引
     */
    private Long cmptUseInx;
    
    /**
     * 事件序列号，用于幂等性控制
     */
    private String seq;
    
    /**
     * 榜单ID
     */
    private Long rankId;
    
    /**
     * 阶段ID
     */
    private Long phaseId;
    
    /**
     * 小时编码，格式：yyyyMMddHH
     */
    private String hourCode;
    
    /**
     * 用户UID
     */
    private Long userUid;
    
    /**
     * 主播UID
     */
    private Long anchorUid;
    
    /**
     * CP分数
     */
    private Long cpScore;
    
    /**
     * 用户昵称
     */
    private String userNick;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 主播昵称
     */
    private String anchorNick;
    
    /**
     * 主播头像
     */
    private String anchorAvatar;
    
    /**
     * 用户多昵称信息JSON
     */
    private String userMultiNick;
    
    /**
     * 主播多昵称信息JSON
     */
    private String anchorMultiNick;
    
    /**
     * 奖励金额，单位：厘
     */
    private Long awardAmount;
    
    /**
     * 奖励配置JSON
     */
    private String awardConfig;
    
    /**
     * 使用的礼物奖池金额，单位：厘
     */
    private Long giftPoolUsed;
    
    /**
     * 奖池是否充足：1-充足，0-不足
     */
    private Boolean isPoolSufficient;
    
    /**
     * 房间SID
     */
    private Long channelSid;
    
    /**
     * 房间SSID
     */
    private Long channelSsid;
    
    /**
     * 是否已发送应援口令：1-已发送，0-未发送
     */
    private Boolean watchwordSent;
    
    /**
     * 是否已广播：1-已广播，0-未广播
     */
    private Boolean broadcastSent;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
