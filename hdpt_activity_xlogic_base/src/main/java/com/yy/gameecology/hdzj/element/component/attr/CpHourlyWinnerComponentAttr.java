package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.*;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件属性配置
 * 
 * <AUTHOR> Generated
 * @date 2025-07-22
 */
@Data
public class CpHourlyWinnerComponentAttr extends ComponentAttr {
    
    /**
     * 业务ID
     */
    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private Long busiId = 200L;
    
    /**
     * CP榜单ID
     */
    @ComponentAttrField(labelText = "CP榜单ID")
    private Long cpRankId;
    
    /**
     * 阶段ID
     */
    @ComponentAttrField(labelText = "阶段ID")
    private Long phaseId;
    
    /**
     * 分数区间奖励配置
     * key: 最小分数，value: 奖励配置
     */
    @ComponentAttrField(labelText = "分数区间奖励配置", 
            subFields = {
                @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "最小分数"),
                @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> scoreRangeAwards = Maps.newLinkedHashMap();
    
    /**
     * 全服礼物奖池总限额，单位：厘
     */
    @ComponentAttrField(labelText = "全服礼物奖池总限额", remark = "单位：厘，40000元=4000000厘")
    private Long giftPoolTotalLimit = 4000000L;
    
    /**
     * 奖池不足时的替代奖励配置
     */
    @ComponentAttrField(labelText = "奖池不足时的替代奖励配置")
    private AwardAttrConfig poolInsufficientAward;
    
    /**
     * 应援口令内容
     */
    @ComponentAttrField(labelText = "应援口令内容")
    private String watchwordContent = "夏日派对，浪漫加倍";
    
    /**
     * 应援口令过期时间（秒）
     */
    @ComponentAttrField(labelText = "应援口令过期时间", remark = "单位：秒")
    private Long watchwordExpireSeconds = 300L;
    
    /**
     * 口令抽奖组件索引
     */
    @ComponentAttrField(labelText = "口令抽奖组件索引")
    private Long watchwordLotteryComponentIndex;
    
    /**
     * 广播业务类型
     */
    @ComponentAttrField(labelText = "广播业务类型", dropDownSourceBeanClass = BroadcastTypeSource.class)
    private Integer broadcastBusiType = 4;
    
    /**
     * 广播模板ID
     */
    @ComponentAttrField(labelText = "广播模板ID")
    private Long broadcastTemplateId;
    
    /**
     * 最新CP组件索引
     */
    @ComponentAttrField(labelText = "最新CP组件索引")
    private Long latestCpComponentIndex;
    
    /**
     * 礼物奖池数据键名
     */
    @ComponentAttrField(labelText = "礼物奖池数据键名")
    private String giftPoolDataKey = "CP_GIFT_POOL_LIMIT";
    
    /**
     * 根据分数查找对应的奖励配置
     * 
     * @param score CP分数
     * @return 奖励配置，如果没有匹配的返回null
     */
    public AwardAttrConfig findAwardByScore(long score) {
        AwardAttrConfig result = null;
        for (Map.Entry<Long, AwardAttrConfig> entry : scoreRangeAwards.entrySet()) {
            Long minScore = entry.getKey();
            if (score >= minScore) {
                result = entry.getValue();
            } else {
                break;
            }
        }
        return result;
    }
    
    /**
     * 获取礼物奖池总限额（厘）
     * 
     * @return 礼物奖池总限额
     */
    public Long getGiftPoolTotalLimitLi() {
        return giftPoolTotalLimit;
    }
    
    /**
     * 获取礼物奖池总限额（元）
     * 
     * @return 礼物奖池总限额，单位：元
     */
    public Double getGiftPoolTotalLimitYuan() {
        return giftPoolTotalLimit / 100.0;
    }
}
