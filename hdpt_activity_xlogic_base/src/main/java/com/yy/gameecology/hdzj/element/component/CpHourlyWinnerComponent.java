package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerComponentDao;
import com.yy.thrift.hdztranking.Rank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件
 * 
 * <AUTHOR> Generated
 * @date 2025-07-22
 */
@Slf4j
@Component
@RestController
@RequestMapping("/5159")
public class CpHourlyWinnerComponent extends BaseActComponent<CpHourlyWinnerComponentAttr> {
    
    @Autowired
    private CpHourlyWinnerComponentDao cpHourlyWinnerComponentDao;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Autowired
    private LatestCpComponent latestCpComponent;
    
    @Override
    public Long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER;
    }
    
    /**
     * 监听榜单结束事件，处理每小时CP榜单第一名结算
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("onRankingTimeEnd start event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        
        // 检查是否是我们关心的榜单
        if (event.getRankId() != attr.getCpRankId()) {
            return;
        }
        
        // 防重检查
        String eventSeq = event.getSeq();
        if (cpHourlyWinnerComponentDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq)) {
            log.warn("Event already processed, skip. eventSeq:{}", eventSeq);
            return;
        }
        
        try {
            // 处理小时榜单结算
            processHourlyRankSettle(event, attr);
        } catch (Exception e) {
            log.error("处理小时榜单结算失败, event:{}, attr:{}", event, attr, e);
            throw e; // 重新抛出异常，触发重试机制
        }
    }
    
    /**
     * 处理小时榜单结算主逻辑
     */
    private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("processHourlyRankSettle start, event:{}, attr:{}", event, attr);

        try {
            // 1. 计算小时编码
            Date endTime = DateUtil.getDate(event.getEndTime());
            String hourCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), endTime);

            // 2. 查询CP榜单第一名
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(
                attr.getActId(),
                attr.getCpRankId(),
                attr.getPhaseId(),
                hourCode,
                1,
                new HashMap<>()
            );

            if (ranks == null || ranks.isEmpty()) {
                log.warn("No ranking data found for hourCode:{}", hourCode);
                return;
            }

            Rank firstRank = ranks.get(0);
            if (firstRank.getScore() <= 0) {
                log.warn("First rank score is zero, skip processing. hourCode:{}", hourCode);
                return;
            }

            // 3. 解析CP成员信息
            CpUid cpUid = Const.splitCpMember(firstRank.getMember());
            Long userUid = cpUid.getUserUid();
            Long anchorUid = cpUid.getAnchorUid();

            log.info("Found first rank CP: userUid:{}, anchorUid:{}, score:{}",
                    userUid, anchorUid, firstRank.getScore());

            // 4. 获取用户和主播信息
//            Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfo(List.of(userUid, anchorUid));
            Map<String, Map<String, MultiNickItem>> multiNickData = new HashMap<>();
            Map<Long, UserInfoVo> userInfoWithNickExt = userInfoService.getUserInfoWithNickExt(
                List.of(userUid, anchorUid), multiNickData, false, Template.all.getCode());

            UserInfoVo userInfo = userInfoWithNickExt.get(userUid);
            UserInfoVo anchorInfo = userInfoWithNickExt.get(anchorUid);

            // 5. 获取第一名CP所在房间信息
            Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(
                attr.getActId(),
                attr.getLatestCpComponentIndex(),
                hourCode,
                attr.getCpRankId(),
                anchorUid
            );

            // 6. 创建数据库记录
            Cmpt5159CpHourlyWinner winner = new Cmpt5159CpHourlyWinner();
            winner.setActId(attr.getActId());
            winner.setCmptUseInx(attr.getCmptUseInx());
            winner.setSeq(event.getSeq());
            winner.setRankId(attr.getCpRankId());
            winner.setPhaseId(attr.getPhaseId());
            winner.setHourCode(hourCode);
            winner.setUserUid(userUid);
            winner.setAnchorUid(anchorUid);
            winner.setCpScore(firstRank.getScore());

            // 设置用户信息
            if (userInfo != null) {
                winner.setUserNick(userInfo.getNick());
                winner.setUserAvatar(userInfo.getAvatarUrl());
            }
            if (anchorInfo != null) {
                winner.setAnchorNick(anchorInfo.getNick());
                winner.setAnchorAvatar(anchorInfo.getAvatarUrl());
            }

            // 设置多昵称信息
            if (multiNickData.containsKey(userUid.toString())) {
                winner.setUserMultiNick(JSON.toJSONString(multiNickData.get(userUid.toString())));
            }
            if (multiNickData.containsKey(anchorUid.toString())) {
                winner.setAnchorMultiNick(JSON.toJSONString(multiNickData.get(anchorUid.toString())));
            }

            // 设置房间信息
            if (latestCp != null) {
                winner.setChannelSid(latestCp.getSid());
                winner.setChannelSsid(latestCp.getSsid());
            }

            // 7. 根据分数区间发放奖励
            AwardAttrConfig awardConfig = attr.findAwardByScore(firstRank.getScore());
            if (awardConfig != null) {
                boolean poolSufficient = checkAndReduceGiftPool(awardConfig.getAwardAmount(), attr, event.getSeq());
                winner.setIsPoolSufficient(poolSufficient);

                if (poolSufficient) {
                    // 奖池充足，发放正常奖励
                    winner.setAwardAmount(awardConfig.getAwardAmount());
                    winner.setAwardConfig(JSON.toJSONString(awardConfig));
                    winner.setGiftPoolUsed(awardConfig.getAwardAmount());
                    giveAwardToWinner(userUid, anchorUid, awardConfig, attr);
                } else {
                    // 奖池不足，发放替代奖励
                    AwardAttrConfig replaceAward = attr.getPoolInsufficientAward();
                    if (replaceAward != null) {
                        winner.setAwardAmount(0L);
                        winner.setAwardConfig(JSON.toJSONString(replaceAward));
                        winner.setGiftPoolUsed(0L);
                        giveAwardToWinner(userUid, anchorUid, replaceAward, attr);
                    }
                }
            }

            // 8. 保存到数据库
            boolean saved = cpHourlyWinnerComponentDao.saveCpHourlyWinner(winner);
            if (!saved) {
                log.error("Failed to save cp hourly winner record");
                return;
            }

            // 9. 发送应援口令到房间
            if (latestCp != null) {
                sendWatchwordToRoom(userUid, anchorUid, attr);
                // 注意：这里需要先查询保存后的记录获取ID，或者修改insert方法返回ID
                // 暂时跳过状态更新，因为ID可能为null
            }

            // 10. 广播第一名CP信息
            broadcastWinnerInfo(winner, attr);
            // 暂时跳过状态更新，因为ID可能为null

            log.info("processHourlyRankSettle completed successfully for hourCode:{}", hourCode);

        } catch (Exception e) {
            log.error("processHourlyRankSettle failed, event:{}, attr:{}", event, attr, e);
            throw e;
        }
    }
    
    /**
     * 查询历史小时冠军CP接口
     */
    @GetMapping("/queryHistoryWinners")
    public Response<Map<String, Object>> queryHistoryWinners(
            @RequestParam("actId") Long actId,
            @RequestParam("cmptUseInx") Long cmptUseInx,
            @RequestParam("dateCode") String dateCode) {
        
        log.info("queryHistoryWinners start, actId:{}, cmptUseInx:{}, dateCode:{}", actId, cmptUseInx, dateCode);
        
        try {
            // TODO: 实现查询历史小时冠军CP的具体逻辑
            // 1. 根据dateCode查询历史冠军记录
            // 2. 组装返回数据，包括用户昵称、用户头像、主播昵称、主播头像
            
            Map<String, Object> result = new HashMap<>();
            result.put("winners", List.of()); // TODO: 替换为实际查询结果
            
            log.info("queryHistoryWinners success, result size: 0");
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("查询历史小时冠军CP失败, actId:{}, cmptUseInx:{}, dateCode:{}", actId, cmptUseInx, dateCode, e);
            return Response.fail(500, "查询失败");
        }
    }
    
    /**
     * 查询全服礼物奖池余额接口
     */
    @GetMapping("/queryGiftPoolBalance")
    public Response<Map<String, Object>> queryGiftPoolBalance(
            @RequestParam("actId") Long actId,
            @RequestParam("cmptUseInx") Long cmptUseInx) {
        
        log.info("queryGiftPoolBalance start, actId:{}, cmptUseInx:{}", actId, cmptUseInx);
        
        try {
            // TODO: 实现查询全服礼物奖池余额的具体逻辑
            // 1. 使用CommonDataDao查询当前奖池使用情况
            // 2. 计算剩余余额
            // 3. 返回奖池余额信息
            
            Map<String, Object> result = new HashMap<>();
            result.put("balance", 1296000L); // TODO: 替换为实际查询结果
            result.put("balanceYuan", 12960.0); // TODO: 替换为实际查询结果
            
            log.info("queryGiftPoolBalance success, balance: 1296000");
            return Response.success(result);
            
        } catch (Exception e) {
            log.error("查询全服礼物奖池余额失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
            return Response.fail(500, "查询失败");
        }
    }
    
    /**
     * 发放奖励给获胜者
     */
    private void giveAwardToWinner(Long userUid, Long anchorUid, AwardAttrConfig awardConfig,
                                   CpHourlyWinnerComponentAttr attr) {
        if (awardConfig == null) {
            log.warn("Award config is null, skip giving award");
            return;
        }

        try {
            // 发放奖励给用户
            if (awardConfig.getTAwardTskId() != null && awardConfig.getTAwardPkgId() != null) {
                // 使用doWelfareV2方法发放奖励
                hdztAwardServiceClient.doWelfareV2(
                    DateUtil.getNowYyyyMMddHHmmss(),
                    attr.getBusiId(),
                    userUid,
                    awardConfig.getTAwardTskId(),
                    awardConfig.getNum() != null ? awardConfig.getNum() : 1,
                    awardConfig.getTAwardPkgId(),
                    "CP_HOURLY_WINNER_" + System.currentTimeMillis(),
                    new HashMap<>()
                );

                log.info("Award given to user successfully: userUid:{}, taskId:{}, packageId:{}, num:{}",
                        userUid, awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId(), awardConfig.getNum());
            }

            // 如果需要也给主播发奖励，可以在这里添加逻辑
            // 目前按照需求，奖励发给CP，这里发给用户

        } catch (Exception e) {
            log.error("Failed to give award to winner, userUid:{}, anchorUid:{}, awardConfig:{}",
                     userUid, anchorUid, awardConfig, e);
            // 不抛出异常，避免影响整个流程
        }
    }
    
    /**
     * 检查并扣减礼物奖池
     */
    private boolean checkAndReduceGiftPool(Long amount, CpHourlyWinnerComponentAttr attr, String seq) {
        if (amount == null || amount <= 0) {
            return true; // 不需要扣减奖池
        }

        try {
            // 使用CommonDataDao.valueIncrIgnoreWithLimit方法实现奖池限额控制
            CommonDataDao.ValueIncResult result = commonDataDao.valueIncrIgnoreWithLimit(
                attr.getActId(),
                this.getComponentId(),
                attr.getCmptUseInx(),
                seq,
                attr.getGiftPoolDataKey(),
                amount,
                attr.getGiftPoolTotalLimitLi()
            );

            boolean sufficient = result.isSuccess();
            log.info("Gift pool check result: amount:{}, sufficient:{}, afterInc:{}, limit:{}",
                    amount, sufficient, result.getAfterInc(), attr.getGiftPoolTotalLimitLi());

            return sufficient;

        } catch (Exception e) {
            log.error("Failed to check and reduce gift pool, amount:{}, seq:{}", amount, seq, e);
            return false; // 出现异常时认为奖池不足，保守处理
        }
    }
    
    /**
     * 发送应援口令到房间
     */
    private void sendWatchwordToRoom(Long userUid, Long anchorUid, CpHourlyWinnerComponentAttr attr) {
        try {
            // 获取第一名CP所在房间信息
            Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(
                attr.getActId(),
                attr.getLatestCpComponentIndex(),
                "", // 不限制日期
                attr.getCpRankId(),
                anchorUid
            );

            if (latestCp == null) {
                log.warn("Cannot find latest cp room info for anchorUid:{}", anchorUid);
                return;
            }

            // 调用ChannelWatchwordLotteryComponent组件添加应援口令抽奖
            // 这里需要根据ChannelWatchwordLotteryComponent的具体API来实现
            // 由于该组件的具体API不在当前上下文中，这里提供一个框架实现

            log.info("Sending watchword to room: sid:{}, ssid:{}, watchword:{}",
                    latestCp.getSid(), latestCp.getSsid(), attr.getWatchwordContent());

            // TODO: 调用ChannelWatchwordLotteryComponent的具体方法
            // 例如：channelWatchwordLotteryComponent.addWatchwordLottery(...)

            log.info("Watchword sent successfully to room: sid:{}, ssid:{}",
                    latestCp.getSid(), latestCp.getSsid());

        } catch (Exception e) {
            log.error("Failed to send watchword to room, userUid:{}, anchorUid:{}", userUid, anchorUid, e);
            // 不抛出异常，避免影响整个流程
        }
    }
    
    /**
     * 广播第一名CP信息
     */
    private void broadcastWinnerInfo(Cmpt5159CpHourlyWinner winner, CpHourlyWinnerComponentAttr attr) {
        try {
            // 构建广播数据
            Map<String, Object> broadcastData = new HashMap<>();
            broadcastData.put("actId", winner.getActId());
            broadcastData.put("hourCode", winner.getHourCode());
            broadcastData.put("cpScore", winner.getCpScore());

            // 用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("uid", winner.getUserUid());
            userInfo.put("nick", winner.getUserNick());
            userInfo.put("avatar", winner.getUserAvatar());
            if (winner.getUserMultiNick() != null) {
                userInfo.put("multiNick", winner.getUserMultiNick());
            }
            broadcastData.put("user", userInfo);

            // 主播信息
            Map<String, Object> anchorInfo = new HashMap<>();
            anchorInfo.put("uid", winner.getAnchorUid());
            anchorInfo.put("nick", winner.getAnchorNick());
            anchorInfo.put("avatar", winner.getAnchorAvatar());
            if (winner.getAnchorMultiNick() != null) {
                anchorInfo.put("multiNick", winner.getAnchorMultiNick());
            }
            broadcastData.put("anchor", anchorInfo);

            // 奖励信息
            if (winner.getAwardConfig() != null) {
                broadcastData.put("awardConfig", winner.getAwardConfig());
            }

            // 根据配置的广播类型进行广播
            if (attr.getBroadcastBusiType() != null) {
                switch (attr.getBroadcastBusiType()) {
                    case 4: // 单业务广播
                        commonBroadCastService.commonNoticeUnicast(
                            winner.getActId(),
                            "cpHourlyWinner",
                            "CP小时第一名",
                            JSON.toJSONString(broadcastData),
                            winner.getUserUid()
                        );
                        break;
                    case 5: // 多业务广播
                        // 这里可以实现多业务广播逻辑
                        log.info("Multi-business broadcast not implemented yet");
                        break;
                    default:
                        log.warn("Unsupported broadcast type: {}", attr.getBroadcastBusiType());
                }
            }

            log.info("Winner info broadcast sent successfully: userUid:{}, anchorUid:{}, hourCode:{}",
                    winner.getUserUid(), winner.getAnchorUid(), winner.getHourCode());

        } catch (Exception e) {
            log.error("Failed to broadcast winner info, winner:{}", winner, e);
            // 不抛出异常，避免影响整个流程
        }
    }
}
