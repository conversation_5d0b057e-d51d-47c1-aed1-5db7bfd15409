package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.common.db.mapper.cmpt.CpHourlyWinnerComponentMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CP小时第一名玩法组件数据访问层
 * 
 * <AUTHOR> Generated
 * @date 2025-07-22
 */
@Slf4j
@Repository
public class CpHourlyWinnerComponentDao {
    
    @Autowired
    private CpHourlyWinnerComponentMapper cpHourlyWinnerComponentMapper;
    
    /**
     * 保存CP小时第一名记录
     * 
     * @param record CP小时第一名记录
     * @return 是否保存成功
     */
    public boolean saveCpHourlyWinner(Cmpt5159CpHourlyWinner record) {
        try {
            int result = cpHourlyWinnerComponentMapper.insert(record);
            return result > 0;
        } catch (Exception e) {
            log.error("保存CP小时第一名记录失败, record:{}", record, e);
            return false;
        }
    }
    
    /**
     * 检查事件是否已处理（幂等性检查）
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param seq 事件序列号
     * @return 是否已处理
     */
    public boolean isEventProcessed(Long actId, Long cmptUseInx, String seq) {
        try {
            int count = cpHourlyWinnerComponentMapper.countBySeq(actId, cmptUseInx, seq);
            return count > 0;
        } catch (Exception e) {
            log.error("检查事件是否已处理失败, actId:{}, cmptUseInx:{}, seq:{}", actId, cmptUseInx, seq, e);
            return false;
        }
    }
    
    /**
     * 根据小时编码前缀查询历史冠军CP
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param hourCodePrefix 小时编码前缀（如：20250721）
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    public List<Cmpt5159CpHourlyWinner> queryHistoryWinnersByPrefix(Long actId, Long cmptUseInx, 
                                                                    String hourCodePrefix, Integer limit) {
        try {
            return cpHourlyWinnerComponentMapper.selectByHourCodePrefix(actId, cmptUseInx, hourCodePrefix, limit);
        } catch (Exception e) {
            log.error("根据小时编码前缀查询历史冠军CP失败, actId:{}, cmptUseInx:{}, hourCodePrefix:{}", 
                     actId, cmptUseInx, hourCodePrefix, e);
            return List.of();
        }
    }
    
    /**
     * 查询指定时间范围内的历史冠军CP
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param startHourCode 开始小时编码
     * @param endHourCode 结束小时编码
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    public List<Cmpt5159CpHourlyWinner> queryHistoryWinnersByRange(Long actId, Long cmptUseInx,
                                                                   String startHourCode, String endHourCode, 
                                                                   Integer limit) {
        try {
            return cpHourlyWinnerComponentMapper.selectByHourCodeRange(actId, cmptUseInx, 
                                                                       startHourCode, endHourCode, limit);
        } catch (Exception e) {
            log.error("查询指定时间范围内的历史冠军CP失败, actId:{}, cmptUseInx:{}, startHourCode:{}, endHourCode:{}", 
                     actId, cmptUseInx, startHourCode, endHourCode, e);
            return List.of();
        }
    }
    
    /**
     * 更新应援口令发送状态
     * 
     * @param id 记录ID
     * @param watchwordSent 是否已发送应援口令
     * @return 是否更新成功
     */
    public boolean updateWatchwordSent(Long id, Boolean watchwordSent) {
        try {
            int result = cpHourlyWinnerComponentMapper.updateWatchwordSent(id, watchwordSent);
            return result > 0;
        } catch (Exception e) {
            log.error("更新应援口令发送状态失败, id:{}, watchwordSent:{}", id, watchwordSent, e);
            return false;
        }
    }
    
    /**
     * 更新广播发送状态
     * 
     * @param id 记录ID
     * @param broadcastSent 是否已广播
     * @return 是否更新成功
     */
    public boolean updateBroadcastSent(Long id, Boolean broadcastSent) {
        try {
            int result = cpHourlyWinnerComponentMapper.updateBroadcastSent(id, broadcastSent);
            return result > 0;
        } catch (Exception e) {
            log.error("更新广播发送状态失败, id:{}, broadcastSent:{}", id, broadcastSent, e);
            return false;
        }
    }
    
    /**
     * 根据ID查询记录
     * 
     * @param id 记录ID
     * @return CP小时第一名记录
     */
    public Cmpt5159CpHourlyWinner getById(Long id) {
        try {
            return cpHourlyWinnerComponentMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID查询记录失败, id:{}", id, e);
            return null;
        }
    }
    
    /**
     * 查询最近的冠军记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    public List<Cmpt5159CpHourlyWinner> queryLatestWinners(Long actId, Long cmptUseInx, Integer limit) {
        try {
            return cpHourlyWinnerComponentMapper.selectLatest(actId, cmptUseInx, limit);
        } catch (Exception e) {
            log.error("查询最近的冠军记录失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
            return List.of();
        }
    }
}
